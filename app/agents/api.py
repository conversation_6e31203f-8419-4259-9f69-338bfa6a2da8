"""
智能体管理的 FastAPI 路由。

该模块包含用于管理智能体的所有 API 端点，
包括 CRUD 操作、聊天功能和智能体配置。
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from typing import List, Optional, Dict, Any
import json

from app.logger.logger import get_logger, PerformanceLogger
from app.database.services import AgentService, ChatService
from app.agents.agent_manager import agent_manager
from .models import (
    CreateAgentRequest,
    UpdateAgentRequest,
    AgentDeleteResponse,
    AgentSetDefaultResponse,
    ChatRequest,
    ChatResponse,
    ChatHistoryResponse,
    ChatMessageResponse
)

# 初始化路由器和日志记录器
router = APIRouter(prefix="/agents", tags=["智能体管理"])
api_logger = get_logger("llm.agent_api")


# 智能体管理端点
@router.get("", response_model=List[Dict[str, Any]], summary="列出所有可用的智能体")
async def list_agents():
    """列出所有可用的智能体。"""
    try:
        agents = await AgentService.list_agents()
        return [agent.to_dict() for agent in agents]
    except Exception as e:
        api_logger.error("从数据库列出智能体失败", exception=e)
        raise HTTPException(status_code=500, detail=f"从数据库列出智能体时出错: {str(e)}")


@router.post("", response_model=Dict[str, Any], summary="创建一个新的智能体")
async def create_agent(request: CreateAgentRequest):
    """创建一个新的智能体。"""
    try:
        with PerformanceLogger(f"api_create_agent_{request.agent_id}", api_logger):
            agent = await agent_manager.create_agent(
                agent_id=request.agent_id,
                agent_type=request.agent_type,
                name=request.name,
                description=request.description,
                system_prompt=request.system_prompt,
                set_as_default=request.set_as_default,
                provider=request.provider,
                model=request.model
            )

            api_logger.info(f"✅ 智能体创建成功: {request.agent_id}")

            # 手动创建智能体信息，因为工作流智能体没有 to_dict()
            agent_info = {
                "name": agent.name,
                "description": agent.description,
                "tools": [tool.metadata.name for tool in (agent.tools or []) if hasattr(tool, 'metadata')],
                "system_prompt": getattr(agent, 'system_prompt', None),
                "type": agent.__class__.__name__
            }

            return {
                "message": f"智能体 '{request.agent_id}' 创建成功",
                "agent": agent_info
            }
    except ValueError as e:
        api_logger.warning(f"智能体创建失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        api_logger.error(f"为 {request.agent_id} 创建智能体失败", exception=e)
        raise HTTPException(status_code=500, detail=f"创建智能体时出错: {str(e)}")


@router.get("/{agent_id}", response_model=Dict[str, Any], summary="获取特定智能体的信息")
async def get_agent(agent_id: str):
    """获取特定智能体的信息。"""
    try:
        agent = await agent_manager.get_agent(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail=f"未找到智能体 '{agent_id}'")

        # 手动创建智能体信息，因为工作流智能体没有 to_dict()
        agent_info = {
            "name": agent.name,
            "description": agent.description,
            "tools": [tool.metadata.name for tool in (agent.tools or []) if hasattr(tool, 'metadata')],
            "system_prompt": getattr(agent, 'system_prompt', None),
            "type": agent.__class__.__name__
        }
        return agent_info
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"获取智能体时出错: {str(e)}")


@router.put("/{agent_id}", response_model=Dict[str, Any], summary="更新一个智能体")
async def update_agent(agent_id: str, request: UpdateAgentRequest):
    """更新一个智能体。"""
    try:
        with PerformanceLogger(f"api_update_agent_{agent_id}", api_logger):
            # 检查智能体是否存在
            agent = agent_manager.get_agent(agent_id)
            if not agent:
                raise HTTPException(status_code=404, detail=f"未找到智能体 '{agent_id}'")

            # 目前，我们将使用更新后的参数重新创建智能体
            # 这是一个简化的方法 - 在完整实现中，
            # 您可能希望向智能体管理器添加更新方法
            
            # 获取当前智能体信息（供将来使用）
            # current_info = agent_manager.list_agents().get(agent_id, {})
            
            # 更新字段
            updates = {k: v for k, v in request.model_dump().items() if v is not None}
            
            if updates:
                api_logger.info(f"已更新智能体: {agent_id}")
                return {"message": f"智能体 '{agent_id}' 更新成功（更新功能待实现）"}
            else:
                raise HTTPException(status_code=400, detail="没有有效的字段可更新")
                
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"更新智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"更新智能体时出错: {str(e)}")


@router.delete("/{agent_id}", response_model=AgentDeleteResponse, summary="删除一个智能体")
async def delete_agent(agent_id: str):
    """删除一个智能体。"""
    try:
        if agent_manager.remove_agent(agent_id):
            api_logger.info(f"已删除智能体: {agent_id}")
            return AgentDeleteResponse(message=f"智能体 '{agent_id}' 删除成功")
        else:
            raise HTTPException(status_code=404, detail=f"未找到智能体 '{agent_id}'")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"删除智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"删除智能体时出错: {str(e)}")


@router.post("/{agent_id}/reset", response_model=Dict[str, str], summary="重置智能体的对话历史")
async def reset_agent(agent_id: str):
    """重置智能体的对话历史。"""
    try:
        if agent_manager.reset_agent(agent_id):
            api_logger.info(f"已重置智能体: {agent_id}")
            return {"message": f"智能体 '{agent_id}' 重置成功"}
        else:
            raise HTTPException(status_code=404, detail=f"未找到智能体 '{agent_id}'")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"重置智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"重置智能体时出错: {str(e)}")


@router.post("/{agent_id}/set-default", response_model=AgentSetDefaultResponse, summary="设置一个智能体为默认")
async def set_default_agent(agent_id: str):
    """设置一个智能体为默认。"""
    try:
        if agent_manager.set_default_agent(agent_id):
            api_logger.info(f"已设置默认智能体: {agent_id}")
            return AgentSetDefaultResponse(message=f"智能体 '{agent_id}' 已成功设为默认")
        else:
            raise HTTPException(status_code=404, detail=f"未找到智能体 '{agent_id}'")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"设置默认智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"设置默认智能体时出错: {str(e)}")

@router.get("/db/{agent_id}", response_model=Dict[str, Any], summary="从数据库获取智能体")
async def get_agent_from_db(agent_id: str):
    """从数据库获取智能体。"""
    try:
        agent = await AgentService.get_agent_by_id(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail=f"在数据库中未找到智能体: {agent_id}")
        return agent.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"从数据库获取智能体失败: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"从数据库获取智能体时出错: {str(e)}")


# 聊天端点
@router.post("/chat", response_model=ChatResponse, summary="与智能体发送消息并获得响应")
async def chat_with_agent(request: ChatRequest):
    """向智能体发送消息并获得响应。"""
    try:
        with PerformanceLogger("api_chat", api_logger):
            api_logger.info(f"📨 聊天请求: agent_id={request.agent_id}, message_length={len(request.message)}")

            response = await agent_manager.chat_with_agent(
                message=request.message,
                agent_id=request.agent_id,
                session_id=request.session_id
            )

            # 获取智能体信息以供响应
            agent = await agent_manager.get_agent(request.agent_id) if request.agent_id else agent_manager.get_default_agent()
            if not agent:
                api_logger.error(f"未找到智能体: {request.agent_id}")
                raise HTTPException(status_code=404, detail="未找到智能体")

            api_logger.info(f"✅ 聊天响应已发送: agent={agent.name}, response_length={len(response)}")

            return ChatResponse(
                response=response,
                agent_id=request.agent_id or "default",
                agent_name=agent.name
            )
    except ValueError as e:
        api_logger.warning(f"聊天请求失败: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        api_logger.error("聊天请求失败", exception=e)
        raise HTTPException(status_code=500, detail=f"处理聊天时出错: {str(e)}")

@router.post("/chat/stream", summary="与智能体发送消息并获得流式响应")
async def stream_chat_with_agent(request: ChatRequest):
    """向智能体发送消息并获得流式响应。"""
    try:
        async def generate_response():
            async for chunk in agent_manager.stream_chat_with_agent(
                message=request.message,
                agent_id=request.agent_id,
                session_id=request.session_id
            ):
                yield f"data: {json.dumps(chunk.model_dump())}\n\n"

        return StreamingResponse(
            generate_response(),
            media_type="text/event-stream",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理流式聊天时出错: {str(e)}")


@router.get("/chat/history", response_model=List[ChatHistoryResponse], summary="列出聊天历史")
async def list_chat_histories(agent_id: Optional[str] = None, limit: int = 50):
    """列出聊天历史。"""
    try:
        histories = await ChatService.list_chat_histories(agent_id=agent_id, limit=limit)
        return [ChatHistoryResponse(**history.to_dict()) for history in histories]
    except Exception as e:
        api_logger.error("列出聊天历史失败", exception=e)
        raise HTTPException(status_code=500, detail=f"列出聊天历史时出错: {str(e)}")


@router.get("/chat/history/{session_id}", response_model=ChatHistoryResponse, summary="获取特定的聊天历史")
async def get_chat_history(session_id: str):
    """获取一个特定的聊天历史。"""
    try:
        history = await ChatService.get_chat_history(session_id)
        if not history:
            raise HTTPException(status_code=404, detail=f"未找到聊天历史: {session_id}")
        return ChatHistoryResponse(**history.to_dict())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取聊天历史 {session_id} 失败", exception=e)
        raise HTTPException(status_code=500, detail=f"获取聊天历史时出错: {str(e)}")


@router.get("/chat/history/{session_id}/messages", response_model=List[ChatMessageResponse], summary="获取聊天会话的消息")
async def get_chat_messages(session_id: str, limit: int = 100):
    """获取一个聊天会话的消息。"""
    try:
        messages = await ChatService.get_messages(session_id, limit=limit)
        return [ChatMessageResponse(**message.to_dict()) for message in messages]
    except Exception as e:
        api_logger.error(f"获取会话 {session_id} 的消息失败", exception=e)
        raise HTTPException(status_code=500, detail=f"获取消息时出错: {str(e)}")
