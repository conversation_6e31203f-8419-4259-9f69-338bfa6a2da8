"""
Pydantic models for Agent API endpoints.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List

# Agent-related models

class CreateAgentRequest(BaseModel):
    """用于创建新代理的请求模型。"""
    agent_id: str = Field(..., description="代理的唯一标识符。")
    agent_type: str = Field("react", description="代理的类型（例如 'react'）。")
    name: Optional[str] = Field(None, description="代理的人性化名称。")
    description: Optional[str] = Field(None, description="代理的描述。")
    system_prompt: Optional[str] = Field(None, description="代理的系统提示。")
    set_as_default: bool = Field(False, description="是否将此代理设置为默认。")
    provider: str = Field(..., description="LLM provider")
    model: str = Field("gpt-3.5-turbo", description="要使用的模型名称。")


class UpdateAgentRequest(BaseModel):
    """用于更新现有代理的请求模型。"""
    name: Optional[str] = Field(None, description="代理的新名称。")
    description: Optional[str] = Field(None, description="代理的新描述。")
    system_prompt: Optional[str] = Field(None, description="代理的新系统提示。")
    set_as_default: Optional[bool] = Field(None, description="是否将此代理设置为默认。")
    provider: Optional[str] = Field(None, description="LLM provider")
    model: Optional[str] = Field(None, description="新的模型名称。")


class AgentResponse(BaseModel):
    """代理信息的响应模型。"""
    agent_id: str = Field(..., description="代理的唯一标识符。")
    name: str = Field(..., description="代理的名称。")
    description: Optional[str] = Field(..., description="代理的描述。")
    agent_type: str = Field(..., description="代理的类型。")
    system_prompt: Optional[str] = Field(..., description="代理的系统提示。")
    model: str = Field(..., description="代理使用的模型。")
    tools: List[str] = Field(..., description="代理使用的工具列表。")
    is_default: bool = Field(..., description="此代理是否为默认代理。")
    is_active: bool = Field(..., description="此代理是否处于活动状态。")
    created_at: str = Field(..., description="创建时间戳。")
    updated_at: str = Field(..., description="最后更新时间戳。")


class AgentSafeResponse(BaseModel):
    """代理信息的安全响应模型（不含敏感数据）。"""
    agent_id: str = Field(..., description="代理的唯一标识符。")
    name: str = Field(..., description="代理的名称。")
    description: Optional[str] = Field(..., description="代理的描述。")
    agent_type: str = Field(..., description="代理的类型。")
    system_prompt: Optional[str] = Field(..., description="代理的系统提示。")
    model: str = Field(..., description="代理使用的模型。")
    tools: List[str] = Field(..., description="代理使用的工具列表。")
    is_default: bool = Field(..., description="此代理是否为默认代理。")
    is_active: bool = Field(..., description="此代理是否处于活动状态。")
    created_at: str = Field(..., description="创建时间戳。")
    updated_at: str = Field(..., description="最后更新时间戳。")


class AgentDeleteResponse(BaseModel):
    """代理删除的响应模型。"""
    message: str = Field(..., description="删除操作的结果消息。")


class AgentSetDefaultResponse(BaseModel):
    """设置默认代理的响应模型。"""
    message: str = Field(..., description="设置默认操作的结果消息。")


class ChatRequest(BaseModel):
    """与代理聊天的请求模型。"""
    message: str = Field(..., description="要发送给代理的消息。")
    agent_id: Optional[str] = Field(None, description="要聊天的代理的 ID（如果未提供，则使用默认代理）。")
    session_id: Optional[str] = Field(None, description="用于维护对话历史记录的会话 ID。")


class ChatResponse(BaseModel):
    """与代理聊天的响应模型。"""
    response: str = Field(..., description="来自代理的响应消息。")
    agent_id: str = Field(..., description="响应该聊天的代理的 ID。")
    agent_name: str = Field(..., description="响应该聊天的代理的名称。")


class ChatHistoryResponse(BaseModel):
    """聊天历史的响应模型。"""
    id: int = Field(..., description="数据库中的唯一 ID。")
    session_id: str = Field(..., description="会话的唯一标识符。")
    agent_id: str = Field(..., description="与此聊天历史关联的代理的 ID。")
    title: Optional[str] = Field(..., description="聊天的标题。")
    created_at: str = Field(..., description="创建时间戳。")
    updated_at: str = Field(..., description="最后更新时间戳。")
    message_count: int = Field(..., description="此聊天历史中的消息数。")


class ChatMessageResponse(BaseModel):
    """聊天消息的响应模型。"""
    id: int = Field(..., description="数据库中的唯一 ID。")
    role: str = Field(..., description="消息的角色（例如 'user', 'assistant'）。")
    content: str = Field(..., description="消息的内容。")
    created_at: str = Field(..., description="创建时间戳。")
    metadata: Optional[Dict[str, Any]] = Field(None, description="与消息关联的元数据。")
