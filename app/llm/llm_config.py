"""
LLM Configuration Management.

This module provides configuration classes and utilities for managing
LLM settings, provider configurations, and validation.
"""

import os
from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List
from enum import Enum


class LLMProviderType(Enum):
    """Supported LLM provider types."""
    OPENAI = "openai"
    OPENAI_COMPATIBLE = "openai_compatible"


@dataclass
class LLMProviderConfig:
    """Configuration for a specific LLM provider."""
    
    provider_type: LLMProviderType
    name: str
    api_base: Optional[str] = None
    api_key: Optional[str] = None
    default_model: str = "gpt-3.5-turbo"
    context_window: int = 4096
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    timeout: int = 60
    max_retries: int = 3
    is_chat_model: bool = True
    is_function_calling_model: bool = False
    extra_params: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.provider_type == LLMProviderType.OPENAI_COMPATIBLE and not self.api_base:
            raise ValueError("api_base is required for OpenAI-compatible providers")
        
        if not self.api_key:
            # Try to get from environment
            env_key = f"{self.name.upper()}_API_KEY"
            self.api_key = os.getenv(env_key)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "provider_type": self.provider_type.value,
            "name": self.name,
            "api_base": self.api_base,
            "api_key": self.api_key,
            "default_model": self.default_model,
            "context_window": self.context_window,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "is_chat_model": self.is_chat_model,
            "is_function_calling_model": self.is_function_calling_model,
            "extra_params": self.extra_params,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "LLMProviderConfig":
        """Create from dictionary."""
        data = data.copy()
        data["provider_type"] = LLMProviderType(data["provider_type"])
        return cls(**data)

    @classmethod
    def from_orm(cls, db_provider: Any) -> "LLMProviderConfig":
        """Create from ORM model."""
        return cls(
            provider_type=LLMProviderType(db_provider.provider_type),
            name=db_provider.provider_id,
            api_base=db_provider.api_base,
            api_key=db_provider.api_key,
            default_model=db_provider.default_model,
            context_window=db_provider.context_window,
            max_tokens=db_provider.max_tokens,
            temperature=db_provider.temperature,
            timeout=db_provider.timeout,
            max_retries=db_provider.max_retries,
            is_chat_model=db_provider.is_chat_model,
            is_function_calling_model=db_provider.is_function_calling_model,
            extra_params=db_provider.extra_params or {},
        )


@dataclass
class LLMConfig:
    """Main LLM configuration class."""
    
    providers: Dict[str, LLMProviderConfig] = field(default_factory=dict)
    default_provider: Optional[str] = "default"
    log_requests: bool = False
    
    def add_provider(self, provider_config: LLMProviderConfig) -> None:
        """Add a new provider configuration."""
        self.providers[provider_config.name] = provider_config
        
        # Set as default if it's the first provider
        if not self.default_provider:
            self.default_provider = provider_config.name
    
    def get_provider(self, name: Optional[str] = None) -> Optional[LLMProviderConfig]:
        """Get provider configuration by name or default."""
        if name is None:
            name = self.default_provider
        
        return self.providers.get(name) if name else None
    
    def remove_provider(self, name: str) -> bool:
        """Remove a provider configuration."""
        if name in self.providers:
            del self.providers[name]
            
            # Update default if removed provider was default
            if self.default_provider == name:
                self.default_provider = next(iter(self.providers.keys())) if self.providers else None
            
            return True
        return False
    
    def list_providers(self) -> List[str]:
        """List all available provider names."""
        return list(self.providers.keys())
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        if not self.providers:
            issues.append("No LLM providers configured")
        
        for name, provider in self.providers.items():
            if not provider.api_key:
                issues.append(f"Provider '{name}' missing API key")
            
            if provider.provider_type == LLMProviderType.OPENAI_COMPATIBLE and not provider.api_base:
                issues.append(f"Provider '{name}' missing API base URL")
        
        if self.default_provider and self.default_provider not in self.providers:
            issues.append(f"Default provider '{self.default_provider}' not found in providers")
        
        return issues
    
    @classmethod
    def from_env(cls) -> "LLMConfig":
        """Create LLM config from environment variables."""
        config = cls()
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "providers": {name: provider.to_dict() for name, provider in self.providers.items()},
            "default_provider": self.default_provider,
            "log_requests": self.log_requests,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "LLMConfig":
        """Create from dictionary."""
        data = data.copy()
        providers = {}
        for name, provider_data in data.get("providers", {}).items():
            providers[name] = LLMProviderConfig.from_dict(provider_data)
        
        data["providers"] = providers
        return cls(**data)
