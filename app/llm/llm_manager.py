"""
LLM Manager for centralized LLM instance management.

This module provides the main LLMManager class that handles LLM instance
creation, caching, and lifecycle management.
"""

import time
import os
import logging
from typing import Dict, Optional, Any, List, Tuple
from threading import Lock
from llama_index.core.llms import LLM

from .llm_config import LLMConfig, LLMProviderConfig, LLMProviderType
from .providers import create_provider, LLMProvider
from app.database.services import LLMService

logger = logging.getLogger("llm.manager")

class LLMManager:
    """Main LLM manager class for centralized LLM management."""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """Initialize LLM manager."""
        self.config = config or LLMConfig.from_env()
        self._providers: Dict[str, LLMProvider] = {}
        self._lock = Lock()
        
        self.logger = logging.getLogger("llm.LLMManager")
        self.logger.info("🚀 LLMManager initialized")
        
        # Initialize providers
        self._initialize_providers()
    
    def _initialize_providers(self) -> None:
        """Initialize provider instances from configuration."""
        for name, provider_config in self.config.providers.items():
            try:
                provider = create_provider(provider_config)
                self._providers[name] = provider
                self.logger.info(f"Initialized provider: {name} ({provider_config.provider_type.value})")
            except Exception:
                self.logger.error(f"Failed to initialize provider {name}", exc_info=True)

        # Create default provider from environment variables if not already configured
        default_api_base = os.getenv("DEFAULT_API_BASE")
        if default_api_base and "default" not in self.config.providers:
            self.logger.info("Found DEFAULT_API_BASE, creating default provider from env vars.")
            provider_config = LLMProviderConfig(
                name="default",
                provider_type=LLMProviderType.OPENAI_COMPATIBLE,
                api_base=default_api_base,
                api_key=os.getenv("DEFAULT_API_KEY"),
                default_model=os.getenv("DEFAULT_MODEL", "gpt-3.5-turbo"),
            )
            try:
                self.config.add_provider(provider_config)
                provider = create_provider(provider_config)
                self._providers[provider_config.name] = provider
                self.logger.info(f"Added provider: {provider_config.name}")

                if not self.config.default_provider:
                    self.config.default_provider = "default"
                    self.logger.info("Set 'default' as the default provider.")
            except Exception:
                self.logger.error(f"Failed to create default provider from env vars", exc_info=True)
    
    async def create_llm(
        self,
        provider_id: Optional[str] = None,
        model: Optional[str] = None,
        **kwargs
    ) -> Tuple[LLM, str, str]:
        """
        Create or retrieve LLM instance.
        
        Args:
            provider_id: Name of the provider to use (uses default if None)
            model: Model name to use (uses provider default if None)
            **kwargs: Additional parameters for LLM creation
        
        Returns:
            Tuple[LLM, str, str]: A tuple containing the LLM instance, the actual provider ID used, and the actual model name used.
        
        Raises:
            ValueError: If provider not found or configuration invalid
        """
        # Use default provider if not specified
        actual_provider_id = provider_id or self.config.default_provider
        
        if not actual_provider_id:
            raise ValueError("No provider specified and no default provider configured")
        
        # Get provider
        provider = self._providers.get(actual_provider_id)
        if not provider:
            # If provider not in cache, try to load from database
            self.logger.info(f"Provider '{actual_provider_id}' not found in cache, trying to load from DB.")
            try:
                db_provider = await LLMService.get_llm_provider_by_id(actual_provider_id)
                if db_provider:
                    provider_config = LLMProviderConfig.from_orm(db_provider)
                    provider = create_provider(provider_config)
                    self._providers[actual_provider_id] = provider
                    self.logger.info(f"Successfully loaded and cached provider '{actual_provider_id}' from DB.")
                else:
                    raise ValueError(f"Provider '{actual_provider_id}' not found in config or database.")
            except Exception as e:
                self.logger.error(f"Failed to load provider '{actual_provider_id}' from DB: {e}", exc_info=True)
                raise ValueError(f"Provider '{actual_provider_id}' not found.")

        # Use provider's default model if not specified
        actual_model = model or provider.config.default_model
        
        # Create new LLM instance
        try:
            llm = provider.create_llm(model=actual_model, **kwargs)
            
            self.logger.info(f"Created LLM: provider={actual_provider_id}, model={actual_model}")
            return llm, actual_provider_id, actual_model
            
        except Exception:
            self.logger.error(f"Failed to create LLM: provider={actual_provider_id}, model={actual_model}", exc_info=True)
            raise
    
    def create_llm_from_params(
        self,
        api_base: Optional[str] = None,
        api_key: Optional[str] = None,
        model: str = "gpt-3.5-turbo",
        **kwargs
    ) -> LLM:
        """
        Create LLM instance from direct parameters.
        
        This method is useful for creating one-off LLM instances without
        pre-configured providers.
        
        Args:
            api_base: API base URL (if None, uses OpenAI)
            api_key: API key
            model: Model name
            **kwargs: Additional parameters
        
        Returns:
            LLM: The LLM instance
        """
        # Determine provider type
        if api_base:
            provider_type = LLMProviderType.OPENAI_COMPATIBLE
        else:
            provider_type = LLMProviderType.OPENAI
        
        # Create temporary provider config
        temp_config = LLMProviderConfig(
            provider_type=provider_type,
            name="temp",
            api_base=api_base,
            api_key=api_key,
            default_model=model,
            **kwargs
        )
        
        # Create provider and LLM
        provider = create_provider(temp_config)
        return provider.create_llm(model=model, **kwargs)
    
    def add_provider(self, provider_config: LLMProviderConfig) -> None:
        """Add a new provider configuration."""
        with self._lock:
            # Add to config
            self.config.add_provider(provider_config)
            
            # Create provider instance
            try:
                provider = create_provider(provider_config)
                self._providers[provider_config.name] = provider
                self.logger.info(f"Added provider: {provider_config.name}")
            except Exception:
                self.logger.error(f"Failed to add provider {provider_config.name}", exc_info=True)
                raise
    
    def remove_provider(self, name: str) -> bool:
        """Remove a provider."""
        with self._lock:
            if name in self._providers:
                del self._providers[name]
                
                # Remove from config
                self.config.remove_provider(name)
                
                self.logger.info(f"Removed provider: {name}")
                return True
        return False
    
    async def list_providers(self) -> List[str]:
        """List all available provider IDs from database."""
        try:
            providers = await LLMService.list_llm_providers()
            return [str(provider.provider_id) for provider in providers]
        except Exception as e:
            self.logger.error(f"Failed to list providers: {e}")
            return []

    async def get_provider_info(self, provider_id: str) -> Optional[LLM]:
        """Get provider information from database."""
        try:
            provider = await LLMService.get_llm_provider_by_id(provider_id)
            if provider:
                # Get provider data and filter out fields that shouldn't be passed to create_llm_from_params
                provider_data = provider.to_dict()

                # Extract the fields needed for create_llm_from_params
                llm_params = {
                    'api_base': provider_data.get('api_base'),
                    'api_key': provider_data.get('api_key'),
                    'model': str(provider.default_model),
                    'temperature': provider_data.get('temperature', 0.7),
                    'max_tokens': provider_data.get('max_tokens'),
                    'timeout': provider_data.get('timeout', 60),
                    'max_retries': provider_data.get('max_retries', 3),
                    'context_window': provider_data.get('context_window', 4096),
                    'is_chat_model': provider_data.get('is_chat_model', True),
                    'is_function_calling_model': provider_data.get('is_function_calling_model', False),
                }

                # Filter out None values
                llm_params = {k: v for k, v in llm_params.items() if v is not None}

                return self.create_llm_from_params(**llm_params)
            return None
        except Exception as e:
            self.logger.error(f"Failed to get provider info for {provider_id}: {e}")
            return None
  
    def validate_configuration(self) -> List[str]:
        """Validate current configuration and return issues."""
        return self.config.validate()

    def reload_config(self, new_config: Optional[LLMConfig] = None) -> None:
        """Reload configuration and reinitialize providers."""
        with self._lock:
            self.config = new_config or LLMConfig.from_env()
            
            # Reinitialize providers
            self._providers.clear()
            self._initialize_providers()
            
            self.logger.info("Reloaded LLM configuration")
