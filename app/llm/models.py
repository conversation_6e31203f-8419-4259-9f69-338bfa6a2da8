"""
Pydantic models for LLM and Agent API endpoints.

This module contains all the request and response models used by the LLM and Agent API endpoints.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List


class CreateLLMProviderRequest(BaseModel):
    """用于创建新的 LLM 供应程序的请求模型。"""
    provider_id: str = Field(..., description="供应程序的唯一标识符。")
    name: str = Field(..., description="供应程序的人性化名称。")
    provider_type: str = Field(..., description="供应程序的类型（例如 openai, openai_compatible 等）。")
    description: Optional[str] = Field(None, description="供应程序的描述。")
    api_base: Optional[str] = Field(None, description="API 的基础 URL。")
    api_key: Optional[str] = Field(None, description="用于验证的 API 密钥。")
    default_model: str = Field("gpt-3.5-turbo", description="此供应程序的默认模型。")
    context_window: int = Field(128000, description="模型的上下文窗口大小。")
    max_tokens: Optional[int] = Field(None, description="生成的最大令牌数。")
    temperature: float = Field(0.7, description="生成的温度。")
    timeout: int = Field(60, description="API 请求的超时时间（秒）。")
    max_retries: int = Field(3, description="API 请求的最大重试次数。")
    is_chat_model: bool = Field(True, description="此模型是否为聊天模型。")
    is_function_calling_model: bool = Field(False, description="此模型是否支持函数调用。")
    set_as_default: bool = Field(False, description="是否将此供应程序设置为默认值。")
    extra_params: Optional[Dict[str, Any]] = Field(None, description="要传递给 LLM 的额外参数。")


class UpdateLLMProviderRequest(BaseModel):
    """用于更新现有 LLM 供应程序的请求模型。"""
    name: Optional[str] = Field(None, description="供应程序的新名称。")
    description: Optional[str] = Field(None, description="供应程序的新描述。")
    api_base: Optional[str] = Field(None, description="新的 API 基础 URL。")
    api_key: Optional[str] = Field(None, description="新的 API 密钥。")
    default_model: Optional[str] = Field(None, description="新的默认模型。")
    context_window: Optional[int] = Field(None, description="新的上下文窗口大小。")
    max_tokens: Optional[int] = Field(None, description="新的最大令牌数。")
    temperature: Optional[float] = Field(None, description="新的生成温度。")
    timeout: Optional[int] = Field(None, description="新的 API 请求超时时间。")
    max_retries: Optional[int] = Field(None, description="新的 API 请求最大重试次数。")
    is_chat_model: Optional[bool] = Field(None, description="此模型是否为聊天模型。")
    is_function_calling_model: Optional[bool] = Field(None, description="此模型是否支持函数调用。")
    set_as_default: Optional[bool] = Field(None, description="是否将此供应程序设置为默认值。")
    extra_params: Optional[Dict[str, Any]] = Field(None, description="要传递给 LLM 的新的额外参数。")


class LLMProviderResponse(BaseModel):
    """LLM 供应程序的完整响应模型（包含敏感信息）。"""
    id: int = Field(..., description="数据库中的唯一 ID。")
    provider_id: str = Field(..., description="供应程序的唯一标识符。")
    name: str = Field(..., description="供应程序的人性化名称。")
    description: Optional[str] = Field(..., description="供应程序的描述。")
    provider_type: str = Field(..., description="供应程序的类型。")
    api_base: Optional[str] = Field(..., description="API 的基础 URL。")
    api_key: Optional[str] = Field(..., description="API 密钥（将在安全版本中屏蔽）。")
    default_model: str = Field(..., description="默认模型。")
    context_window: int = Field(..., description="上下文窗口大小。")
    max_tokens: Optional[int] = Field(..., description="最大令牌数。")
    temperature: float = Field(..., description="生成温度。")
    timeout: int = Field(..., description="API 请求超时时间。")
    max_retries: int = Field(..., description="API 请求最大重试次数。")
    is_chat_model: bool = Field(..., description="是否为聊天模型。")
    is_function_calling_model: bool = Field(..., description="是否支持函数调用。")
    is_active: bool = Field(..., description="供应程序是否处于活动状态。")
    is_default: bool = Field(..., description="是否为默认供应程序。")
    extra_params: Optional[Dict[str, Any]] = Field(..., description="额外的 LLM 参数。")
    created_at: str = Field(..., description="创建时间戳。")
    updated_at: str = Field(..., description="最后更新时间戳。")


class LLMProviderSafeResponse(BaseModel):
    """LLM 供应程序的安全响应模型（不含敏感信息）。"""
    id: int = Field(..., description="数据库中的唯一 ID。")
    provider_id: str = Field(..., description="供应程序的唯一标识符。")
    name: str = Field(..., description="供应程序的人性化名称。")
    description: Optional[str] = Field(..., description="供应程序的描述。")
    provider_type: str = Field(..., description="供应程序的类型。")
    api_base: Optional[str] = Field(..., description="API 的基础 URL。")
    default_model: str = Field(..., description="默认模型。")
    context_window: int = Field(..., description="上下文窗口大小。")
    max_tokens: Optional[int] = Field(..., description="最大令牌数。")
    temperature: float = Field(..., description="生成温度。")
    timeout: int = Field(..., description="API 请求超时时间。")
    max_retries: int = Field(..., description="API 请求最大重试次数。")
    is_chat_model: bool = Field(..., description="是否为聊天模型。")
    is_function_calling_model: bool = Field(..., description="是否支持函数调用。")
    is_active: bool = Field(..., description="供应程序是否处于活动状态。")
    is_default: bool = Field(..., description="是否为默认供应程序。")
    extra_params: Optional[Dict[str, Any]] = Field(..., description="额外的 LLM 参数。")
    created_at: str = Field(..., description="创建时间戳。")
    updated_at: str = Field(..., description="最后更新时间戳。")


class LLMProviderDeleteResponse(BaseModel):
    """LLM 供应程序删除的响应模型。"""
    message: str = Field(..., description="删除操作的结果消息。")


class LLMProviderSetDefaultResponse(BaseModel):
    """设置默认 LLM 供应程序的响应模型。"""
    message: str = Field(..., description="设置默认操作的结果消息。")



